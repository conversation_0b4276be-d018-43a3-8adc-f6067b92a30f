<template>
    <section class="home-block">
        <div class="header">
            <h2 class="title">{{ title }}</h2>
            <p class="more" v-if="more" @click="onClick">
                <span>{{ more }}</span>
                <span class="icon-arrow" />
            </p>
        </div>
        <div class="content">
            <slot />
        </div>
    </section>
</template>

<script setup>
import { toRefs } from 'vue'

const props = defineProps({
    title: {
        type: String,
        required: true
    },
    more: {
        type: String,
        default: ''
    }
})

const emit = defineEmits(['click'])

const { title, more } = toRefs(props)

const onClick = () => {
    emit('click')
}
</script>

<style lang="less" scoped>
.home-block {
    margin-top: 24px;

    .header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 16px;
        padding: 0 16px;

        .title {
            font-size: 20px;
            font-weight: 600;
            color: #171e24;
            line-height: 1.4;
        }

        .more {
            display: flex;
            align-items: center;
            font-size: 13px;
            font-weight: 300;
            color: #5a6066;
            cursor: pointer;
            transition: color 0.2s ease;

            &:hover {
                color: #333;
            }

            .icon-arrow {
                margin-left: 2px;
                width: 11px;
                height: 11px;
                background-image: url('./assets/arrow.png');
                background-repeat: no-repeat;
                background-size: contain;
                background-position: center;
                transition: transform 0.2s ease;
            }

            &:hover .icon-arrow {
                transform: translateX(2px);
            }
        }
    }

    .content {
        padding: 0 10px;
    }
}
</style>

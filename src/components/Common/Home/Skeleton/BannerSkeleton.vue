<template>
  <div class="banner-skeleton">
    <div class="skeleton-banner">
      <div class="skeleton-image"></div>
    </div>
  </div>
</template>

<script setup>
// 骨架屏组件不需要任何逻辑
</script>

<style scoped lang="less">
// 骨架屏动画
@keyframes skeleton-loading {
  0% {
    background-position: -200px 0;
  }
  100% {
    background-position: calc(200px + 100%) 0;
  }
}

.skeleton-base {
  background: linear-gradient(90deg, #f5f5f5 25%, #e8e8e8 50%, #f5f5f5 75%);
  background-size: 200px 100%;
  animation: skeleton-loading 1.2s ease-in-out infinite;
  border-radius: 4px;
}

.banner-skeleton {
  margin: 8px 12px;
  border-radius: 12px;
  overflow: hidden;
  //box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);

  .skeleton-banner {
    position: relative;
    width: 100%;
    height: 200px;
    background: #f8f9fa;

    .skeleton-image {
      .skeleton-base();
      width: 100%;
      height: 100%;
      border-radius: 12px;
    }

    .skeleton-pagination {
      position: absolute;
      bottom: 12px;
      right: 12px;
      display: flex;
      gap: 6px;
      background: rgba(0, 0, 0, 0.3);
      padding: 4px 8px;
      border-radius: 12px;

      .skeleton-dot {
        .skeleton-base();
        width: 6px;
        height: 6px;
        border-radius: 50%;
        background: rgba(255, 255, 255, 0.6);
        animation: none;
      }
    }
  }
}
</style>

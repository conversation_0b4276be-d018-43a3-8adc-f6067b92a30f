<template>
  <div class="grid-menu-skeleton">
    <div class="skeleton-grid-container">
      <div v-for="i in 5" :key="i" class="skeleton-grid-item">
        <div class="skeleton-icon"></div>
        <div class="skeleton-title"></div>
        <div class="skeleton-subtitle"></div>
      </div>
    </div>
  </div>
</template>

<script setup>
// 骨架屏组件不需要任何逻辑
</script>

<style scoped lang="less">
// 骨架屏动画
@keyframes skeleton-loading {
  0% {
    background-position: -200px 0;
  }
  100% {
    background-position: calc(200px + 100%) 0;
  }
}

.skeleton-base {
  background: linear-gradient(90deg, #f5f5f5 25%, #e8e8e8 50%, #f5f5f5 75%);
  background-size: 200px 100%;
  animation: skeleton-loading 1.2s ease-in-out infinite;
  border-radius: 4px;
}

.grid-menu-skeleton {
  margin: 8px 12px;
  background: #ffffff;
  border-radius: 12px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
  padding: 5px;

  .skeleton-grid-container {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;

    .skeleton-grid-item {
      width: calc(20% - 6.4px); // 5列布局，减去gap的影响
      display: flex;
      flex-direction: column;
      align-items: center;
      padding: 16px 8px;
      text-align: center;

      .skeleton-icon {
        .skeleton-base();
        width: 40px;
        height: 40px;
        border-radius: 8px;
        margin-bottom: 8px;
      }

      .skeleton-title {
        .skeleton-base();
        width: 80%;
        height: 14px;
        margin-bottom: 4px;
      }

      .skeleton-subtitle {
        .skeleton-base();
        width: 60%;
        height: 12px;
      }
    }
  }
}

// 移动端适配
@media (max-width: 375px) {
  .grid-menu-skeleton {
    .skeleton-grid-container {
      .skeleton-grid-item {
        padding: 12px 6px;

        .skeleton-icon {
          width: 36px;
          height: 36px;
          margin-bottom: 6px;
        }

        .skeleton-title {
          height: 12px;
          margin-bottom: 3px;
        }

        .skeleton-subtitle {
          height: 10px;
        }
      }
    }
  }
}
</style>

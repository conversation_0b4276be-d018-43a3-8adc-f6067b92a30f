<template>
  <div class="horizontal-scroll-skeleton">
    <div class="skeleton-scroll-wrapper">
      <div v-for="i in skeletonCount" :key="i" class="skeleton-item">
        <div class="skeleton-image"></div>
        <div class="skeleton-content">
          <div class="skeleton-title"></div>
          <div class="skeleton-title short"></div>
          <div class="skeleton-details">
            <div class="skeleton-price"></div>
            <div class="skeleton-sales"></div>
          </div>
          <div class="skeleton-spec"></div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
const props = defineProps({
  skeletonCount: {
    type: Number,
    default: 5
  }
})
</script>

<style scoped lang="less">
// 骨架屏动画
@keyframes skeleton-loading {
  0% {
    background-position: -200px 0;
  }
  100% {
    background-position: calc(200px + 100%) 0;
  }
}

.skeleton-base {
  background: linear-gradient(90deg, #f5f5f5 25%, #e8e8e8 50%, #f5f5f5 75%);
  background-size: 200px 100%;
  animation: skeleton-loading 1.2s ease-in-out infinite;
  border-radius: 4px;
}

.horizontal-scroll-skeleton {
  padding: 0 12px;
  min-height: 180px;
  display: flex;
  align-items: center;

  .skeleton-scroll-wrapper {
    display: flex;
    gap: 12px;
    overflow-x: auto;
    padding-bottom: 8px;
    width: 100%;

    // 隐藏滚动条但保持滚动功能
    &::-webkit-scrollbar {
      display: none;
    }

    -ms-overflow-style: none;
    scrollbar-width: none;

    .skeleton-item {
      flex: 0 0 160px;
      background: #fff;
      border-radius: 12px;
      overflow: hidden;
      box-shadow: 0 1px 3px rgba(0, 0, 0, 0.08);
      height: 260px;

      // 最后一个元素添加右边距
      &:last-child {
        margin-right: 12px;
      }

      .skeleton-image {
        .skeleton-base();
        width: 100%;
        height: 160px;
        border-radius: 12px 12px 0 0;
      }

      .skeleton-content {
        padding: 16px 14px 14px;

        .skeleton-title {
          .skeleton-base();
          height: 15px;
          margin-bottom: 8px;
          width: 100%;

          &.short {
            width: 70%;
            margin-bottom: 12px;
          }
        }

        .skeleton-details {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 8px;
          gap: 8px;

          .skeleton-price {
            .skeleton-base();
            height: 20px;
            width: 60px;
            flex-shrink: 0;
          }

          .skeleton-sales {
            .skeleton-base();
            height: 16px;
            width: 50px;
            border-radius: 10px;
          }
        }

        .skeleton-spec {
          .skeleton-base();
          height: 14px;
          width: 80%;
          border-radius: 8px;
        }
      }
    }
  }
}

// 移动端适配
@media (max-width: 375px) {
  .horizontal-scroll-skeleton {
    .skeleton-scroll-wrapper {
      gap: 10px;

      .skeleton-item {
        flex: 0 0 150px;
        height: 240px;

        .skeleton-image {
          height: 140px;
        }

        .skeleton-content {
          padding: 14px 12px 12px;

          .skeleton-title {
            height: 14px;
            margin-bottom: 6px;

            &.short {
              margin-bottom: 10px;
            }
          }

          .skeleton-details {
            .skeleton-price {
              height: 18px;
              width: 55px;
            }

            .skeleton-sales {
              height: 14px;
              width: 45px;
            }
          }

          .skeleton-spec {
            height: 12px;
          }
        }
      }
    }
  }
}
</style>

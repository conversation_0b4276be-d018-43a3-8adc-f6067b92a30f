import { createRouter, createWebHistory } from 'vue-router'
import { afterEachScroll, afterEachSetTitle, beforeEachLoginInterceptor } from 'commonkit'
import { getBizTitle } from "@utils/curEnv.js";
import {handleUrlParameters, setEntryQuerystring} from "@utils/entryQuerystring.js";
import { loginType } from '@utils/storage.js'
import WoMallRouter from '@router/WoMall/index.js'
const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes: [
    {
      path: '/',
      redirect: '/Home'
    },
    {
      path: '/home',
      name: 'home',
      component: () => import('@views/Home/BFHome/IndexView.vue'),
    },
    ...WoMallRouter
  ],
})

// 登录拦截器
router.beforeEach((to, from, next) => {
  beforeEachLoginInterceptor({
    to,
    from,
    next,
    loginRouteName: 'login',
    autoQueryStatus: false,
    loginType: loginType.get() || '0'
  })
})

router.afterEach((to, from) => {
  // 设置入口查询字符串
  setEntryQuerystring()

  // 处理URL参数
  handleUrlParameters(to)

  // 处理页面不能滚动问题
  document.body.style.overflow = ''

  // 设置页面标题
  afterEachSetTitle(to, getBizTitle())

  // 处理页面滚动
  afterEachScroll()
})
  export default router

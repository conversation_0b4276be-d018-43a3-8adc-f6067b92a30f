<template>
  <div class="bf-home">
    <SearchHeader v-model="searchKeyword" placeholder="搜索商品" :redirect-to-search="true" redirect-url="/search"
      @search="handleSearch" />

    <!-- Banner轮播 -->
    <div class="banner-container">
      <!-- Banner骨架屏 -->
      <Transition name="skeleton-fade" mode="out-in">
        <BannerSkeleton v-if="skeletonStates.banner" key="banner-skeleton" />
        <!-- Banner实际内容 -->
        <GoodsImageSwiper v-else-if="headerBannerList.length > 0" key="banner-content" :media-list="headerBannerList"
          mode="banner" paginationType="fraction" :autoplay="true" :loop="true" height="200px"
          @image-click="handleBannerClick" />
      </Transition>
    </div>

    <!-- 宫格菜单 -->
    <div class="grid-menu-container">
      <!-- 宫格菜单骨架屏 -->
      <Transition name="skeleton-fade" mode="out-in">
        <GridMenuSkeleton v-if="skeletonStates.gridMenu" key="grid-skeleton" />
        <!-- 宫格菜单实际内容 -->
        <GridMenu v-else-if="gridMenuItems.length > 0" key="grid-content" :items="gridMenuItems" :columns="5"
          :show-more="true" :max-items="10" @item-click="handleGridItemClick" @more-click="handleMoreClick" />
      </Transition>
    </div>

    <Block title="各县销冠" ref="limitedBlockRef">
      <van-list :loading="false" :finished="true" :immediate-check="false">
        <div class="waterfall-container" :style="{ minHeight: limitedList.length > 0 ? 'auto' : '200px' }">
          <!-- 各县销冠骨架屏和实际内容 -->
          <Transition name="skeleton-fade" mode="out-in">
            <WaterfallSkeleton v-if="skeletonStates.limited" key="limited-skeleton" :skeleton-count="6" />
            <!-- 各县销冠实际内容 -->
            <Waterfall v-else-if="limitedList.length > 0" key="limited-content" ref="limitedWaterfallRef"
              :list="limitedList" :breakpoints="breakpoints" :hasAroundGutter="false" :animationDuration="300"
              :animationDelay="50" :backgroundColor="'transparent'" :lazyload="true">
              <template #default="{ item }">
                <GoodsCard :key="item.goodsId" :goods-info="item" @click="handleGoodsClick(item)" />
              </template>
            </Waterfall>
          </Transition>
        </div>

        <!-- 各县销冠加载更多按钮 -->
        <div class="load-more-container" v-if="limitedList.length > 0 && !limitedFinished">
          <van-button
            type="text"
            :loading="limitedLoading"
            @click="handleLimitedLoadMore"
            class="load-more-button"
          >
            {{ limitedLoading ? '加载中...' : '加载更多' }}
          </van-button>
        </div>

        <!-- 没有更多数据提示 -->
        <div class="no-more-text" v-if="limitedList.length > 0 && limitedFinished">
          <span>没有更多了</span>
        </div>
      </van-list>
    </Block>

    <Transition name="module-fade" appear>
      <Block v-if="moduleShowStates.newer" title="新上好物" ref="newerBlockRef">
        <div class="horizontal-scroll-container" :style="{ minHeight: '180px' }">
          <!-- 新上好物骨架屏和实际内容 -->
          <Transition name="skeleton-fade" mode="out-in">
            <HorizontalScrollSkeleton v-if="skeletonStates.newer" key="newer-skeleton" :skeleton-count="5" />
            <!-- 新上好物实际内容 -->
            <div v-else-if="newerList.length > 0" key="newer-content" class="horizontal-scroll-wrapper">
              <div class="goods-item" v-for="item in newerList" :key="item.goodsId" @click="handleGoodsClick(item)">
                <GoodsCard :goods-info="item" @click="handleGoodsClick(item)" />
              </div>
            </div>
          </Transition>
        </div>
      </Block>
    </Transition>

    <Transition name="module-fade" appear>
      <Block v-if="moduleShowStates.hotProducts" title="爆款好物" ref="hotProductsBlockRef">
        <van-list :loading="false" :finished="true" :immediate-check="false">
          <div class="waterfall-container" :style="{ minHeight: hotProductsList.length > 0 ? 'auto' : '200px' }">
            <!-- 爆款好物骨架屏和实际内容 -->
            <Transition name="skeleton-fade" mode="out-in">
              <WaterfallSkeleton v-if="skeletonStates.hotProducts" key="hot-skeleton" :skeleton-count="6" />
              <!-- 爆款好物实际内容 -->
              <Waterfall v-else-if="hotProductsList.length > 0" key="hot-content" ref="hotProductsWaterfallRef"
                :list="hotProductsList" :breakpoints="breakpoints" :hasAroundGutter="false" :animationDuration="300"
                :animationDelay="50" :backgroundColor="'transparent'" :lazyload="true">
                <template #default="{ item }">
                  <GoodsCard :key="item.goodsId" :goods-info="item" @click="handleGoodsClick(item)" />
                </template>
              </Waterfall>
            </Transition>
          </div>

          <!-- 爆款好物加载更多按钮 -->
          <div class="load-more-container" v-if="hotProductsList.length > 0 && !hotProductsFinished">
            <van-button
              type="text"
              :loading="hotProductsLoading"
              @click="handleHotProductsLoadMore"
              class="load-more-button"
            >
              {{ hotProductsLoading ? '加载中...' : '加载更多' }}
            </van-button>
          </div>

          <!-- 没有更多数据提示 -->
          <div class="no-more-text" v-if="hotProductsList.length > 0 && hotProductsFinished">
            <span>没有更多了</span>
          </div>
        </van-list>
      </Block>
    </Transition>
  </div>
</template>
<script setup>
import { ref, onMounted, onUnmounted, nextTick } from 'vue'
import { Waterfall } from 'vue-waterfall-plugin-next'
import 'vue-waterfall-plugin-next/dist/style.css'
import SearchHeader from '@components/Common/SearchHeader.vue'
import GoodsImageSwiper from '@/components/Common/GoodsImageSwiper.vue'
import GridMenu from '@components/Common/Home/GridMenu.vue'
import { getBannerInfo, getIconInfo } from '@/api/interface/bannerIcon'
import { getGoodsList } from '@/api/interface/goods'
import { getBizCode } from '@/utils/curEnv'
import { curChannelBiz } from '@/utils/storage'
import { isUnicom, isWopay } from 'commonkit'
import Block from '@components/Common/Home/Block.vue'
import GoodsCard from '@components/Common/Home/GoodsCard.vue'
import { fenToYuan } from '@utils/amount.js'
import { useRouter } from 'vue-router'
import { closeToast, showLoadingToast } from 'vant'
// 导入骨架屏组件
import BannerSkeleton from '@components/Common/Home/Skeleton/BannerSkeleton.vue'
import GridMenuSkeleton from '@components/Common/Home/Skeleton/GridMenuSkeleton.vue'
import WaterfallSkeleton from '@components/Common/Home/Skeleton/WaterfallSkeleton.vue'
import HorizontalScrollSkeleton from '@components/Common/Home/Skeleton/HorizontalScrollSkeleton.vue'
const router = useRouter()
// 基础数据
const searchKeyword = ref('')
const headerBannerList = ref([])

// 宫格菜单数据
const gridMenuItems = ref([])

// 各县销冠数据
const limitedList = ref([])
const limitedLoading = ref(false)
const limitedFinished = ref(false)
const limitedCurrentPage = ref(1)
const limitedPageSize = ref(10)
const limitedLastLoadTime = ref(0)
const limitedIsFirstLoadComplete = ref(false)

// 新上好物数据
const newerList = ref([])
const newerLoading = ref(false)
const newerIsComplete = ref(false)

// 爆款好物数据
const hotProductsList = ref([])
const hotProductsLoading = ref(false)
const hotProductsFinished = ref(false)
const hotProductsCurrentPage = ref(1)
const hotProductsPageSize = ref(10)
const hotProductsLastLoadTime = ref(0)
const hotProductsIsFirstLoadComplete = ref(false)

// 骨架屏显示状态控制 - 按顺序显示和消失
const skeletonStates = ref({
  banner: true,      // Banner轮播骨架屏
  gridMenu: true,    // 宫格菜单骨架屏
  limited: true,     // 各县销冠骨架屏
  newer: true,       // 新上好物骨架屏
  hotProducts: true  // 爆款好物骨架屏
})

// 模块显示状态控制 - 只有完全准备好才显示
const moduleShowStates = ref({
  limited: true,     // 各县销冠始终显示（第一个模块）
  newer: false,      // 新上好物是否可以显示
  hotProducts: false // 爆款好物是否可以显示
})

// 模块数据准备状态
const moduleDataReady = ref({
  banner: false,     // Banner轮播数据是否准备完成
  gridMenu: false,   // 宫格菜单数据是否准备完成
  limited: false,    // 各县销冠数据是否准备完成
  newer: false,      // 新上好物数据是否准备完成
  hotProducts: false // 爆款好物数据是否准备完成
})

// 瀑布流配置
const breakpoints = ref({
  750: { rowPerView: 2 },
  550: { rowPerView: 2 },
  375: { rowPerView: 2 },
  290: { rowPerView: 1 }
})

// 瀑布流容器引用
const limitedWaterfallRef = ref(null)
const hotProductsWaterfallRef = ref(null)

// 模块DOM引用，用于视口检测
const limitedBlockRef = ref(null)
const newerBlockRef = ref(null)
const hotProductsBlockRef = ref(null)

// 视口检测相关状态
const intersectionObserver = ref(null)
const moduleInViewport = ref({
  limited: false,    // 各县销冠是否在视口内
  newer: false,      // 新上好物是否在视口内
  hotProducts: false // 爆款好物是否在视口内
})

// banner 过滤对应渠道的数据
const channelFilterd = list => {
  if (isUnicom) {
    return list.filter(item => item.channelType === '1')
  } else if (isWopay) {
    return list.filter(item => item.channelType === '0')
  } else {
    return list.filter(item => item.channelType === '2')
  }
}

// 骨架屏顺序控制函数
const hideSkeletonInOrder = async () => {
  // 按顺序隐藏骨架屏：Banner轮播 → 宫格菜单 → 各县销冠 → 新上好物 → 爆款好物

  // 1. Banner轮播骨架屏消失
  if (moduleDataReady.value.banner && skeletonStates.value.banner) {
    skeletonStates.value.banner = false
    console.log('Banner轮播骨架屏消失')
    await new Promise(resolve => setTimeout(resolve, 200)) // 等待过渡动画
  }

  // 2. 宫格菜单骨架屏消失
  if (moduleDataReady.value.gridMenu && skeletonStates.value.gridMenu) {
    skeletonStates.value.gridMenu = false
    console.log('宫格菜单骨架屏消失')
    await new Promise(resolve => setTimeout(resolve, 200))
  }

  // 3. 各县销冠骨架屏消失
  if (moduleDataReady.value.limited && skeletonStates.value.limited) {
    skeletonStates.value.limited = false
    console.log('各县销冠骨架屏消失')
    await new Promise(resolve => setTimeout(resolve, 200))
  }

  // 4. 新上好物骨架屏消失
  if (moduleDataReady.value.newer && skeletonStates.value.newer) {
    skeletonStates.value.newer = false
    console.log('新上好物骨架屏消失')
    await new Promise(resolve => setTimeout(resolve, 200))
  }

  // 5. 爆款好物骨架屏消失
  if (moduleDataReady.value.hotProducts && skeletonStates.value.hotProducts) {
    skeletonStates.value.hotProducts = false
    console.log('爆款好物骨架屏消失')
  }
}

// 获取头部banner列表
const getHeaderBannerList = async () => {
  const [err, json] = await getBannerInfo({ bizCode: getBizCode('QUERY'), showPage: 1 })
  console.warn(213132, err, json)
  if (!err) {
    // 转换数据格式为GoodsImageSwiper组件需要的格式
    const bannerData = channelFilterd(json).map(item => ({
      type: 'image',
      url: item.imgUrl,
      alt: item.bannerChName,
      linkUrl: item.url,
    }))
    console.warn(1231323123, bannerData)
    headerBannerList.value = bannerData
  }

  // 标记Banner数据准备完成并触发骨架屏顺序隐藏
  moduleDataReady.value.banner = true
  await hideSkeletonInOrder()
}

// 获取宫格菜单列表
const getIconList = async () => {
  const [err, json] = await getIconInfo({
    bizCode: getBizCode('QUERY'),
    channel: curChannelBiz.get(),
    showPage: 2
  })

  if (!err) {
    if (json) {
      const iconData = json.map(item => ({
        title: item.chName || item.title,
        subtitle: item.iconSubTitle || item.subtitle,
        icon: item.imgUrl || item.icon,
        url: item.url,
        badge: item.badge || item.iconBadge
      }))
      gridMenuItems.value = iconData.slice(0, 4)
    } else {
      gridMenuItems.value = []
    }
  }

  // 标记宫格菜单数据准备完成并触发骨架屏顺序隐藏
  moduleDataReady.value.gridMenu = true
  await hideSkeletonInOrder()
}

// 搜索处理
const handleSearch = () => {
  // 搜索功能由 SearchHeader 组件处理
}

// Banner点击处理
const handleBannerClick = ({ item }) => {
  if (item.linkUrl) {
    // 处理banner点击跳转逻辑
    window.location.href = item.linkUrl
  }
}

// 宫格菜单点击处理
const handleGridItemClick = ({ item, index }) => {
  console.log('点击宫格菜单:', item, index)
  if (item.url) {
    // 这里可以使用 Vue Router 进行路由跳转
    // router.push(item.url)
    // 或者直接跳转
    window.location.href = item.url
  }
}

// 更多按钮点击处理
const handleMoreClick = () => {
  console.log('点击更多按钮')
  // 跳转到分类页面或显示更多菜单
  // router.push('/category')
}

// 获取各县销冠列表
const getLimitedList = async (isLoadMore = false) => {
  if (limitedLoading.value) return

  limitedLoading.value = true
  if (!isLoadMore) {
    showLoadingToast()
  }

  const [err, json] = await getGoodsList({
    type: 'partion',
    bizCode: getBizCode('GOODS'),
    page_no: limitedCurrentPage.value,
    page_size: limitedPageSize.value,
    id: import.meta.env.VITE_FP_HOME_PAGE_LIMITED_GOODS_ID
  })

  if (!isLoadMore) {
    closeToast()
  }

  if (!err && json && Array.isArray(json)) {
    // 转换数据格式
    const newItems = json.map(item => ({
      name: item.name || item.goodName,
      price: fenToYuan(item.skuList[0].price) || item.goodsPrice,
      sales: item.skuList[0].realSaleVolume || item.salesCount || 0,
      goodsId: item.id || item.goodsId,
      image: item.listImageUrl || item.image,
      spec: item.spec || item.goodsSpec,
    }))

    // 预加载图片以减少布局跳跃
    await preloadImages(newItems.map(item => item.image))

    if (isLoadMore) {
      limitedList.value = [...limitedList.value, ...newItems]
      // 等待DOM更新
      await nextTick()
      // 等待 Waterfall 重新计算布局完成
      await waitForWaterfallRender(limitedWaterfallRef, 3000)
    } else {
      limitedList.value = newItems
      await nextTick()

      // 等待 Waterfall 完全渲染完成
      await waitForWaterfallRender(limitedWaterfallRef)

      limitedIsFirstLoadComplete.value = true

      // 首次加载完成后，标记模块完成并触发骨架屏顺序隐藏和下一个模块加载
      if (!moduleDataReady.value.limited) {
        moduleDataReady.value.limited = true
        console.log('各县销冠模块完全渲染完成，开始隐藏骨架屏并准备下一个模块')

        // 触发骨架屏顺序隐藏
        await hideSkeletonInOrder()

        // 等待一小段时间确保瀑布流布局稳定
        await new Promise(resolve => setTimeout(resolve, 300))

        // 触发新上好物模块显示和视口检测设置
        await loadNextModule()

        console.log('各县销冠模块处理完成，新上好物模块已准备就绪')
      }
    }

    // 只有当返回的数据为空数组时才停止加载
    if (json.length === 0) {
      limitedFinished.value = true
    }

    // 只有在成功加载数据时才递增页码
    if (isLoadMore) {
      limitedCurrentPage.value++
    } else {
      // 首次加载成功后，将页码设置为2，为下次加载做准备
      limitedCurrentPage.value = 2
    }

  } else {
    // 没有数据或出错时，标记为加载完成
    limitedFinished.value = true
    // 即使失败也要触发骨架屏隐藏和下一个模块
    if (!moduleDataReady.value.limited) {
      moduleDataReady.value.limited = true
      await hideSkeletonInOrder()
      await loadNextModule()
    }
  }

  limitedLoading.value = false
}
// 各县销冠加载更多处理
const handleLimitedLoadMore = () => {
  if (!limitedFinished.value && !limitedLoading.value && limitedIsFirstLoadComplete.value) {
    getLimitedList(true)
  }
}

// 获取新上好物列表
const getNewerList = async () => {
  if (newerLoading.value) return

  newerLoading.value = true

  // 不显示全局loading，因为模块内部有loading状态
  const [err, json] = await getGoodsList({
    type: 'partion',
    bizCode: getBizCode('GOODS'),
    page_no: 1,
    page_size: 10,
    id: import.meta.env.VITE_FP_HOME_PAGE_NEWER_GOODS_ID
  })

  if (!err && json && Array.isArray(json)) {
    // 转换数据格式
    const newItems = json.map(item => ({
      name: item.name || item.goodName,
      price: fenToYuan(item.skuList[0].price) || item.goodsPrice,
      sales: item.skuList[0].realSaleVolume || item.salesCount || 0,
      goodsId: item.id || item.goodsId,
      image: item.listImageUrl || item.image,
      spec: item.spec || item.goodsSpec,
    }))

    // 预加载图片以减少布局跳跃
    await preloadImages(newItems.map(item => item.image))

    newerList.value = newItems
    await nextTick()

    // 等待一小段时间确保横向滚动列表渲染完成
    await new Promise(resolve => setTimeout(resolve, 300))
  }

  // 标记新上好物模块完成，然后触发骨架屏隐藏和下一个模块显示
  if (!moduleDataReady.value.newer) {
    moduleDataReady.value.newer = true
    newerIsComplete.value = true

    console.log('新上好物模块渲染完成，开始隐藏骨架屏并显示下一个模块')

    // 等待一帧确保DOM更新
    await nextTick()

    // 触发骨架屏顺序隐藏
    await hideSkeletonInOrder()
    // 触发下一个模块显示（不是加载数据）
    await loadNextModule()
  }

  newerLoading.value = false
}

// 获取爆款好物列表
const getHotProductsList = async (isLoadMore = false) => {
  if (hotProductsLoading.value) return

  hotProductsLoading.value = true

  // 只在加载更多时显示全局loading，首次加载使用模块内部loading
  if (isLoadMore) {
    showLoadingToast()
  }

  const [err, json] = await getGoodsList({
    type: 'partion',
    bizCode: getBizCode('GOODS'),
    page_no: hotProductsCurrentPage.value,
    page_size: hotProductsPageSize.value,
    id: import.meta.env.VITE_FP_HOME_PAGE_GUESS_GOODS_ID
  })

  if (isLoadMore) {
    closeToast()
  }

  if (!err && json && Array.isArray(json)) {
    // 转换数据格式为GoodsWaterfallItem组件需要的格式
    const newItems = json.map(item => ({
      name: item.name || item.goodName,
      price: fenToYuan(item.skuList[0].price) || item.goodsPrice,
      sales: item.skuList[0].realSaleVolume || item.salesCount || 0,
      goodsId: item.id || item.goodsId,
      image: item.listImageUrl || item.image,
      spec: item.spec || item.goodsSpec,
    }))

    // 预加载图片以减少布局跳跃
    await preloadImages(newItems.map(item => item.image))

    if (isLoadMore) {
      hotProductsList.value = [...hotProductsList.value, ...newItems]
      // 等待DOM更新
      await nextTick()
      // 等待 Waterfall 重新计算布局完成
      await waitForWaterfallRender(hotProductsWaterfallRef, 3000)
    } else {
      hotProductsList.value = newItems
      await nextTick()

      // 等待 Waterfall 完全渲染完成
      await waitForWaterfallRender(hotProductsWaterfallRef)

      hotProductsIsFirstLoadComplete.value = true

      // 首次加载完成后，标记模块完成并隐藏骨架屏
      if (!moduleDataReady.value.hotProducts) {
        moduleDataReady.value.hotProducts = true

        // 触发骨架屏顺序隐藏
        await hideSkeletonInOrder()
        // 显示爆款好物模块
        moduleShowStates.value.hotProducts = true
        console.log('爆款好物模块渲染完成，骨架屏隐藏并显示')
      }
    }

    // 只有当返回的数据为空数组时才停止加载
    if (json.length === 0) {
      hotProductsFinished.value = true
    }

    // 只有在成功加载数据时才递增页码
    if (isLoadMore) {
      hotProductsCurrentPage.value++
    } else {
      // 首次加载成功后，将页码设置为2，为下次加载做准备
      hotProductsCurrentPage.value = 2
    }
  } else {
    // 没有数据或出错时，标记为加载完成
    hotProductsFinished.value = true
    // 即使失败也要标记模块完成、隐藏骨架屏并显示
    if (!moduleDataReady.value.hotProducts) {
      moduleDataReady.value.hotProducts = true
      await hideSkeletonInOrder()
      moduleShowStates.value.hotProducts = true
    }
  }

  hotProductsLoading.value = false
}

// 爆款好物加载更多处理
const handleHotProductsLoadMore = () => {
  if (!hotProductsFinished.value && !hotProductsLoading.value && hotProductsIsFirstLoadComplete.value) {
    getHotProductsList(true)
  }
}

// 图片预加载函数，减少布局跳跃
const preloadImages = (imageUrls) => {
  return Promise.all(
    imageUrls.map(url => {
      return new Promise((resolve) => {
        if (!url) {
          resolve()
          return
        }
        const img = new Image()
        img.onload = () => resolve()
        img.onerror = () => resolve() // 即使加载失败也继续
        img.src = url
        // 设置超时，避免长时间等待
        setTimeout(() => resolve(), 3000)
      })
    })
  )
}

// 等待 Waterfall 渲染完成的函数
const waitForWaterfallRender = async (waterfallRef, timeout = 5000) => {
  return new Promise((resolve) => {
    let checkCount = 0
    const maxChecks = timeout / 100 // 每100ms检查一次

    const checkRender = () => {
      checkCount++

      // 如果没有瀑布流引用，直接完成
      if (!waterfallRef.value) {
        resolve()
        return
      }

      // 检查瀑布流是否已经渲染完成
      const waterfallEl = waterfallRef.value.$el || waterfallRef.value
      if (waterfallEl && waterfallEl.children && waterfallEl.children.length > 0) {
        // 检查是否所有子元素都有正确的位置
        const children = Array.from(waterfallEl.children)
        const allPositioned = children.every(child => {
          const style = window.getComputedStyle(child)
          return style.transform !== 'none' || (style.left !== 'auto' && style.top !== 'auto')
        })

        if (allPositioned) {
          console.log('Waterfall 渲染完成')
          resolve()
          return
        }
      }

      // 如果超时或达到最大检查次数，强制完成
      if (checkCount >= maxChecks) {
        console.log('Waterfall 渲染检查超时，强制继续')
        resolve()
        return
      }

      // 继续检查
      setTimeout(checkRender, 100)
    }

    // 开始检查
    setTimeout(checkRender, 100)
  })
}

const handleGoodsClick = (goodsInfo) => {
  console.log('点击商品:', goodsInfo)
  if (goodsInfo.goodsId) {
    // 跳转到商品详情页
    router.push(`/goodsdetail/${goodsInfo.goodsId}`)
  }
}

// 视口检测功能
const setupIntersectionObserver = () => {
  // 创建 Intersection Observer
  intersectionObserver.value = new IntersectionObserver(
    (entries) => {
      entries.forEach((entry) => {
        const target = entry.target
        const isIntersecting = entry.isIntersecting

        // 根据元素的 ref 属性判断是哪个模块
        if (target === limitedBlockRef.value?.$el) {
          moduleInViewport.value.limited = isIntersecting
          if (isIntersecting) {
            console.log('各县销冠模块进入视口')
          }
        } else if (target === newerBlockRef.value?.$el) {
          moduleInViewport.value.newer = isIntersecting
          if (isIntersecting) {
            console.log('新上好物模块进入视口，检查加载条件')
            // 只有在满足所有前置条件时才加载
            if (canLoadNewerModule()) {
              console.log('新上好物模块满足所有前置条件，开始加载')
              checkAndLoadNextModule('newer')
            } else {
              console.log('新上好物模块前置条件未满足，等待各县销冠完成')
            }
          }
        } else if (target === hotProductsBlockRef.value?.$el) {
          moduleInViewport.value.hotProducts = isIntersecting
          if (isIntersecting) {
            console.log('爆款好物模块进入视口')
            // 检查是否可以加载爆款好物
            checkAndLoadNextModule('hotProducts')
          }
        }
      })
    },
    {
      // 当模块距离视口底部 100px 时就开始加载
      rootMargin: '0px 0px 100px 0px',
      threshold: 0.1
    }
  )
}

// 开始观察模块
const observeModules = () => {
  if (!intersectionObserver.value) return

  // 观察各县销冠模块
  if (limitedBlockRef.value?.$el) {
    intersectionObserver.value.observe(limitedBlockRef.value.$el)
  }

  // 使用专门的函数来设置新上好物模块观察器
  setupNewerModuleObserver()

  // 观察爆款好物模块（如果已显示）
  if (hotProductsBlockRef.value?.$el) {
    intersectionObserver.value.observe(hotProductsBlockRef.value.$el)
  }
}

// 检查是否可以观察新上好物模块
const canObserveNewerModule = () => {
  // 前置条件：
  // 1. 各县销冠模块数据已准备完成
  // 2. 各县销冠模块首次加载已完成
  // 3. 瀑布流组件已完成渲染
  // 4. 新上好物模块已显示
  const canObserve = moduleDataReady.value.limited &&
                     limitedIsFirstLoadComplete.value &&
                     moduleShowStates.value.newer

  if (canObserve) {
    console.log('新上好物模块满足观察条件')
  }

  return canObserve
}

// 专门为新上好物模块设置观察器
const setupNewerModuleObserver = () => {
  if (!intersectionObserver.value || !newerBlockRef.value?.$el) {
    console.log('观察器或新上好物模块DOM未准备好')
    return
  }

  if (canObserveNewerModule()) {
    intersectionObserver.value.observe(newerBlockRef.value.$el)
    console.log('✅ 新上好物模块观察器设置成功')
  } else {
    console.log('❌ 新上好物模块前置条件未满足，暂不设置观察器')
  }
}

// 检查并加载下一个模块
const checkAndLoadNextModule = async (moduleName) => {
  if (moduleName === 'newer') {
    // 新上好物模块进入视口，检查是否可以加载
    // 严格检查前置条件：各县销冠完全完成 + 瀑布流渲染完成 + 模块在视口内 + 数据未加载
    if (canLoadNewerModule() && moduleInViewport.value.newer && !moduleDataReady.value.newer) {
      console.log('新上好物模块满足所有条件，开始加载数据')
      await getNewerList()
    }
  } else if (moduleName === 'hotProducts') {
    // 爆款好物模块进入视口，检查是否可以加载
    if (moduleDataReady.value.newer && !moduleDataReady.value.hotProducts && moduleInViewport.value.hotProducts) {
      console.log('爆款好物模块在视口内，开始加载数据')
      await getHotProductsList(false)
    }
  }
}

// 检查是否可以加载新上好物模块数据
const canLoadNewerModule = () => {
  // 严格的前置条件检查：
  // 1. 各县销冠模块数据已准备完成
  // 2. 各县销冠模块首次加载已完成
  // 3. 瀑布流组件已完成布局渲染
  // 4. 新上好物模块已显示
  const limitedComplete = moduleDataReady.value.limited && limitedIsFirstLoadComplete.value
  const newerModuleReady = moduleShowStates.value.newer

  console.log('检查新上好物加载条件:', {
    limitedDataReady: moduleDataReady.value.limited,
    limitedFirstLoadComplete: limitedIsFirstLoadComplete.value,
    newerModuleShown: moduleShowStates.value.newer,
    canLoad: limitedComplete && newerModuleReady
  })

  return limitedComplete && newerModuleReady
}

// 控制模块顺序加载的核心函数（修改为支持视口检测）
const loadNextModule = async () => {
  // 如果各县销冠完成了，显示新上好物模块（但不立即加载数据）
  if (moduleDataReady.value.limited && limitedIsFirstLoadComplete.value && !moduleShowStates.value.newer) {
    console.log('各县销冠完全完成，显示新上好物模块')
    moduleShowStates.value.newer = true
    await nextTick()

    // 等待DOM更新后，专门为新上好物模块设置视口检测
    setTimeout(() => {
      console.log('开始为新上好物模块设置视口检测')
      setupNewerModuleObserver()
    }, 300) // 增加延迟确保DOM完全更新和瀑布流布局稳定
  }

  // 如果新上好物完成了，显示爆款好物模块（但不立即加载数据）
  if (moduleDataReady.value.newer && !moduleShowStates.value.hotProducts) {
    console.log('显示爆款好物模块')
    moduleShowStates.value.hotProducts = true
    await nextTick()

    // 等待DOM更新后开始观察新模块
    setTimeout(() => {
      observeModules()
    }, 100)
  }
}



// 组件挂载时获取数据
onMounted(() => {
  getHeaderBannerList()
  getIconList()

  // 设置视口检测
  setupIntersectionObserver()

  // 只加载第一个模块：各县销冠数据
  // 其他模块会在前一个模块完成后自动触发显示，并通过视口检测决定是否加载数据
  getLimitedList(false)

  // 等待DOM渲染完成后开始观察模块
  nextTick(() => {
    setTimeout(() => {
      observeModules()
    }, 500) // 给足够时间让组件完全渲染
  })
})

// 组件卸载时清理资源
onUnmounted(() => {
  if (intersectionObserver.value) {
    intersectionObserver.value.disconnect()
    intersectionObserver.value = null
  }
})
</script>

<style scoped lang="less">
.bf-home {
  width: 100vw;
  overflow: hidden;
  .banner-container {
    margin: 8px 12px;
    border-radius: 12px;
    overflow: hidden;
    //box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
  }

  .grid-menu-container {
    background: #ffffff;
    border-radius: 12px;
    margin: 8px 12px;
    //box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
  }

  .horizontal-scroll-container {
    //padding: 0 12px;
    position: relative;
    // 设置最小高度以防止布局跳动
    min-height: 180px;
    display: flex;
    align-items: center;

    .horizontal-scroll-wrapper {
      display: flex;
      gap: 12px;
      overflow-x: auto;
      padding-bottom: 8px;
      scroll-behavior: smooth;
      width: 100%;

      // 隐藏滚动条但保持滚动功能
      &::-webkit-scrollbar {
        display: none;
      }

      -ms-overflow-style: none;
      scrollbar-width: none;

      .goods-item {
        flex: 0 0 160px;
        cursor: pointer;

        // 最后一个元素添加右边距
        &:last-child {
          margin-right: 12px;
        }
      }
    }
  }

  .waterfall-container {
    transition: min-height 0.3s ease;

    // 为瀑布流提供稳定的容器
    :deep(.vue-waterfall) {
      transition: height 0.3s ease;
    }
  }

  .loading-placeholder {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 40px 0;
    color: #969799;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 100%;

    // 确保在横向滚动容器中居中显示
    .horizontal-scroll-container & {
      position: static;
      transform: none;
      padding: 60px 0;
    }
  }

  // 模块显示过渡动画 - 优化动画效果
  .module-fade-enter-active {
    transition: all 0.6s cubic-bezier(0.25, 0.8, 0.25, 1);
  }

  .module-fade-leave-active {
    transition: all 0.3s ease-in;
  }

  .module-fade-enter-from {
    opacity: 0;
    transform: translateY(30px);
  }

  .module-fade-leave-to {
    opacity: 0;
    transform: translateY(-10px);
  }

  .module-fade-enter-to,
  .module-fade-leave-from {
    opacity: 1;
    transform: translateY(0);
  }

  // 模块容器样式优化
  .home-block {
    // 确保模块之间有适当的间距
    &:not(:first-child) {
      margin-top: 32px;
    }

    // 为过渡动画提供稳定的布局
    .content {
      position: relative;
      overflow: hidden;
    }
  }

  // 骨架屏过渡动画
  .skeleton-fade-enter-active,
  .skeleton-fade-leave-active {
    transition: all 0.3s ease;
  }

  .skeleton-fade-enter-from,
  .skeleton-fade-leave-to {
    opacity: 0;
    transform: translateY(10px);
  }

  .skeleton-fade-enter-to,
  .skeleton-fade-leave-from {
    opacity: 1;
    transform: translateY(0);
  }

  // 加载更多按钮样式
  .load-more-container {
    padding: 20px 16px 16px;
    display: flex;
    justify-content: center;

    .load-more-button {
      min-width: 120px;
      height: 36px;
      font-size: 14px;
      color: #1989fa;

      // text类型按钮的自定义样式
      :deep(.van-button__text) {
        color: #1989fa;
      }

      &:active {
        opacity: 0.7;
      }
    }
  }

  // 没有更多数据提示样式
  .no-more-text {
    padding: 20px 0 16px;
    text-align: center;

    span {
      font-size: 14px;
      color: #969799;
    }
  }
}
</style>
